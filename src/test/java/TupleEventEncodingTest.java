import org.junit.jupiter.api.Test;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.DynamicArray;
import org.web3j.abi.datatypes.DynamicStruct;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.datatypes.generated.Bytes32;

import java.util.Arrays;

public class TupleEventEncodingTest {

    @Test
    public void testForceBurnEventEncoding() {
        // Create ForceBurn event with tuple[] parameter
        // Event: ForceBurn(tuple[] burnData, bytes32 traceId)
        
        TypeReference<DynamicArray<DynamicStruct>> tupleArrayRef = 
            new TypeReference<DynamicArray<DynamicStruct>>(false) {};
        
        TypeReference<Bytes32> traceIdRef = 
            new TypeReference<Bytes32>(false) {};
        
        Event forceBurnEvent = new Event("ForceBurn", 
            Arrays.asList(tupleArrayRef, traceIdRef));
        
        // Try to encode the event signature
        String signature = EventEncoder.encode(forceBurnEvent);
        
        System.out.println("ForceBurn event signature: " + signature);
        
        // Verify it doesn't throw an exception
        assert signature != null;
        assert !signature.isEmpty();
    }
    
    @Test
    public void testSimpleTupleEventEncoding() {
        // Create a simple event with tuple parameter
        // Event: SimpleEvent(tuple data)
        
        TypeReference<DynamicStruct> tupleRef = 
            new TypeReference<DynamicStruct>(false) {};
        
        Event simpleEvent = new Event("SimpleEvent", 
            Arrays.asList(tupleRef));
        
        // Try to encode the event signature
        String signature = EventEncoder.encode(simpleEvent);
        
        System.out.println("SimpleEvent signature: " + signature);
        
        // Verify it doesn't throw an exception
        assert signature != null;
        assert !signature.isEmpty();
    }
}
