import java.lang.reflect.Method;
import org.web3j.protocol.core.methods.response.AbiDefinition;

public class TupleTest {
  public static void main(String[] args) {
    // Test what methods are available in AbiDefinition.NamedType
    AbiDefinition.NamedType namedType = new AbiDefinition.NamedType();

    System.out.println("Available methods in AbiDefinition.NamedType:");
    for (Method method : namedType.getClass().getMethods()) {
      if (method.getName().startsWith("get") || method.getName().startsWith("is")) {
        System.out.println(
            "- " + method.getName() + "(): " + method.getReturnType().getSimpleName());
      }
    }

    // Test tuple classes
    System.out.println("\nTuple classes available:");
    String[] tupleClasses = {
      "org.web3j.abi.datatypes.StaticStruct",
      "org.web3j.abi.datatypes.DynamicStruct",
      "org.web3j.abi.datatypes.Struct",
      "org.web3j.tuples.Tuple",
      "org.web3j.tuples.Tuple2",
      "org.web3j.tuples.Tuple3"
    };

    for (String className : tupleClasses) {
      try {
        Class.forName(className);
        System.out.println("- " + className + " available");
      } catch (ClassNotFoundException e) {
        System.out.println("- " + className + " NOT available");
      }
    }
  }
}
